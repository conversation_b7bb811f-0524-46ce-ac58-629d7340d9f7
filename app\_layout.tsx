import { Stack, useRouter, useSegments } from 'expo-router';
import { AuthProvider, useAuth } from '@src/context/AuthContext';
import { StatusBar } from 'expo-status-bar';
import React, { useState, useEffect } from 'react';
import * as SplashScreen from 'expo-splash-screen';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

const InitialLayout = () => {
  const { user } = useAuth();
  const segments = useSegments();
  const router = useRouter();
  const [isRouterReady, setIsRouterReady] = useState(false);

  useEffect(() => {
    // A small timer to ensure the layout is mounted before we do anything.
    const timer = setTimeout(() => setIsRouterReady(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isRouterReady) {
      return; // Don't do anything until the router is ready
    }

    const inAuthGroup = segments[0] === '(auth)';

    if (!user && !inAuthGroup) {
      // Use requestAnimationFrame to defer navigation until the next frame
      requestAnimationFrame(() => {
        router.replace('/(auth)/login');
      });
    } else if (user && inAuthGroup) {
      requestAnimationFrame(() => {
        router.replace('/(tabs)');
      });
    }

    // Hide the splash screen now that we're done.
    SplashScreen.hideAsync();

  }, [user, isRouterReady, segments, router]);

  // Render the navigator. The useEffect hook will handle redirection.
  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="settings" options={{ presentation: 'modal' }} />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
};

export default function RootLayout() {
  return (
    <AuthProvider>
      <InitialLayout />
    </AuthProvider>
  );
}