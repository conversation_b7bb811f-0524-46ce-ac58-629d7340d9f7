import React, { createContext, useState, useMemo, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { AppTheme, lightTheme, darkTheme } from '../(tabs)/community/styles/theme';

type ThemeContextType = {
  theme: AppTheme;
  themePreference: 'light' | 'dark' | 'system';
  setTheme: (themeName: 'light' | 'dark' | 'system') => void;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const systemTheme = useColorScheme() ?? 'light';
  const [themePreference, setThemePreference] = useState<'light' | 'dark' | 'system'>('system');

  // Load saved theme preference from storage
  useEffect(() => {
    // TODO: Load theme preference from AsyncStorage or similar
    // const loadThemePreference = async () => {
    //   try {
    //     const savedTheme = await AsyncStorage.getItem('themePreference');
    //     if (savedTheme === 'light' || savedTheme === 'dark' || savedTheme === 'system') {
    //       setThemePreference(savedTheme);
    //     }
    //   } catch (error) {
    //     console.error('Failed to load theme preference', error);
    //   }
    // };
    // loadThemePreference();
  }, []);

  // Save theme preference when it changes
  useEffect(() => {
    // TODO: Save theme preference to AsyncStorage or similar
    // const saveThemePreference = async () => {
    //   try {
    //     await AsyncStorage.setItem('themePreference', themePreference);
    //   } catch (error) {
    //     console.error('Failed to save theme preference', error);
    //   }
    // };
    // saveThemePreference();
  }, [themePreference]);

  const theme = useMemo(() => {
    if (themePreference === 'system') {
      return systemTheme === 'dark' ? darkTheme : lightTheme;
    }
    return themePreference === 'dark' ? darkTheme : lightTheme;
  }, [systemTheme, themePreference]);

  const toggleTheme = () => {
    setThemePreference(prev => {
      if (prev === 'system') return 'dark';
      if (prev === 'dark') return 'light';
      return 'system';
    });
  };

  const value = useMemo(
    () => ({
      theme,
      themePreference,
      setTheme: setThemePreference,
      toggleTheme,
    }),
    [theme, themePreference]
  );

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook for easy consumption
export const useAppTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context;
};

// Export the context for advanced use cases
export { ThemeContext };
