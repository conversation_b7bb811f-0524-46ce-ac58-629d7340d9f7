{"name": "my-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "npx expo start", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "test": "jest --watchAll"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-community/datetimepicker": "8.3.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/native": "^7.1.11", "expo": "^53.0.0", "expo-asset": "~11.1.5", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-image": "~2.3.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "victory-native": "^37.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~51.0.1", "react-native-svg-transformer": "^1.5.1", "typescript": "~5.3.3"}, "private": true}